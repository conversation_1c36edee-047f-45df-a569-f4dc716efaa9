#!/usr/bin/env python3
"""
Test script to verify that all WSGI components have been successfully removed
and replaced with ASGI equivalents.
"""

import ast
import os
import sys
import importlib
from pathlib import Path

def find_wsgi_references(directory):
    """Find any remaining WSGI references in Python files."""
    wsgi_files = []
    
    # WSGI patterns to look for
    wsgi_patterns = [
        'ThreadedWSGIServer',
        'BaseWSGIServer',
        'WSGIServer',
        'WSGIHandler',
        'WSGIRequestHandler',
        'LoggingBaseWSGIServerMixIn',
        'ThreadedWSGIServerReloadable',
        'BaseWSGIServerNoBind',
        'from werkzeug.serving import',
        'werkzeug.serving.ThreadedWSGIServer',
        'werkzeug.serving.BaseWSGIServer',
        'gevent.pywsgi.WSGIServer',
        'gevent.wsgi.WSGIServer',
    ]
    
    for root, dirs, files in os.walk(directory):
        # Skip virtual environment and cache directories
        dirs[:] = [d for d in dirs if d not in ('venv', '__pycache__', '.git', 'node_modules')]
        
        for file in files:
            if file.endswith('.py') and file != 'test_wsgi_removal.py':  # Exclude this test script
                file_path = os.path.join(root, file)
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        
                    for line_no, line in enumerate(content.split('\n'), 1):
                        for pattern in wsgi_patterns:
                            if pattern in line and not line.strip().startswith('#'):
                                wsgi_files.append((file_path, line.strip(), line_no))
                                
                except (UnicodeDecodeError, PermissionError):
                    continue
    
    return wsgi_files

def test_asgi_imports():
    """Test that ASGI components can be imported successfully."""
    test_imports = [
        'odoo.http.asgi_application',
        'odoo.service.asgi_server',
        'odoo.http',  # Should now export ASGI root
    ]
    
    failed_imports = []
    
    for module_name in test_imports:
        try:
            module = importlib.import_module(module_name)
            print(f"✅ Successfully imported {module_name}")
            
            # Test specific components
            if module_name == 'odoo.http.asgi_application':
                assert hasattr(module, 'AsyncApplication'), "AsyncApplication not found"
                assert hasattr(module, 'async_root'), "async_root not found"
                print(f"  ✅ AsyncApplication and async_root available")
                
            elif module_name == 'odoo.service.asgi_server':
                assert hasattr(module, 'AsyncServer'), "AsyncServer not found"
                assert hasattr(module, 'start_asgi_server'), "start_asgi_server not found"
                print(f"  ✅ AsyncServer and start_asgi_server available")
                
            elif module_name == 'odoo.http':
                assert hasattr(module, 'root'), "root not found"
                print(f"  ✅ root (ASGI) available")
                
        except ImportError as e:
            failed_imports.append((module_name, str(e)))
            print(f"❌ Failed to import {module_name}: {e}")
        except AssertionError as e:
            failed_imports.append((module_name, str(e)))
            print(f"❌ {module_name}: {e}")
    
    return failed_imports

def test_configuration():
    """Test that configuration is properly set for ASGI."""
    config_file = Path('odoo.conf')
    
    if not config_file.exists():
        print("⚠️  odoo.conf not found")
        return False
    
    with open(config_file, 'r') as f:
        content = f.read()
    
    # Check for ASGI configuration
    if 'asgi_enable = True' in content:
        print("✅ ASGI enabled in configuration")
        return True
    else:
        print("❌ ASGI not enabled in configuration")
        return False

def test_server_startup():
    """Test basic server startup (import test only)."""
    try:
        # Test that we can import the server start function
        from odoo.service.server import start
        print("✅ Server start function can be imported")
        
        # Test that ASGI server can be imported
        from odoo.service.asgi_server import start_asgi_server
        print("✅ ASGI server start function can be imported")
        
        return True
    except ImportError as e:
        print(f"❌ Failed to import server components: {e}")
        return False

def main():
    """Main test function."""
    print("🔍 Testing WSGI to ASGI migration...")
    print("=" * 50)
    
    all_tests_passed = True
    
    # Test 1: Check for remaining WSGI references
    print("\n1. Checking for remaining WSGI references...")
    wsgi_refs = find_wsgi_references('.')
    if wsgi_refs:
        print("❌ Found remaining WSGI references:")
        for file_path, line, line_no in wsgi_refs[:10]:  # Show first 10
            print(f"  {file_path}:{line_no} - {line}")
        if len(wsgi_refs) > 10:
            print(f"  ... and {len(wsgi_refs) - 10} more")
        all_tests_passed = False
    else:
        print("✅ No WSGI references found")
    
    # Test 2: Test ASGI imports
    print("\n2. Testing ASGI component imports...")
    failed_imports = test_asgi_imports()
    if failed_imports:
        all_tests_passed = False
    
    # Test 3: Test configuration
    print("\n3. Testing configuration...")
    config_ok = test_configuration()
    if not config_ok:
        all_tests_passed = False
    
    # Test 4: Test server startup imports
    print("\n4. Testing server startup...")
    startup_ok = test_server_startup()
    if not startup_ok:
        all_tests_passed = False
    
    # Summary
    print("\n" + "=" * 50)
    if all_tests_passed:
        print("🎉 All tests passed! WSGI to ASGI migration appears successful.")
        return 0
    else:
        print("❌ Some tests failed. Please review the issues above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
